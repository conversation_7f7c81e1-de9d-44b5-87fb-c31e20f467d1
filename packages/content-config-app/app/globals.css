@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@100..900&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

/* :root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

:root {
    --background: oklch(0.9383 0.0042 236.4993);
    --foreground: oklch(0.3211 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.3211 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.3211 0 0);
    --primary: oklch(0.6397 0.172 36.4421);
    --primary-foreground: oklch(1 0 0);
    --secondary: oklch(0.967 0.0029 264.5419);
    --secondary-foreground: oklch(0.4461 0.0263 256.8018);
    --muted: oklch(0.9846 0.0017 247.8389);
    --muted-foreground: oklch(0.551 0.0234 264.3637);
    --accent: oklch(0.9119 0.0222 243.8174);
    --accent-foreground: oklch(0.3791 0.1378 265.5222);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1 0 0);
    --border: oklch(0.9022 0.0052 247.8822);
    --input: oklch(0.97 0.0029 264.542);
    --ring: oklch(0.6397 0.172 36.4421);
    --chart-1: oklch(0.7156 0.0605 248.6845);
    --chart-2: oklch(0.7875 0.0917 35.9616);
    --chart-3: oklch(0.5778 0.0759 254.1573);
    --chart-4: oklch(0.5016 0.0849 259.4902);
    --chart-5: oklch(0.4241 0.0952 264.0306);
    --sidebar: oklch(0.903 0.0046 258.3257);
    --sidebar-foreground: oklch(0.3211 0 0);
    --sidebar-primary: oklch(0.6397 0.172 36.4421);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.9119 0.0222 243.8174);
    --sidebar-accent-foreground: oklch(0.3791 0.1378 265.5222);
    --sidebar-border: oklch(0.9276 0.0058 264.5313);
    --sidebar-ring: oklch(0.6397 0.172 36.4421);
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0em;
    --spacing: 0.25rem;
  }

  .dark {
    --background: oklch(0.2598 0.0306 262.6666);
    --foreground: oklch(0.9219 0 0);
    --card: oklch(0.3106 0.0301 268.6365);
    --card-foreground: oklch(0.9219 0 0);
    --popover: oklch(0.29 0.0249 268.3986);
    --popover-foreground: oklch(0.9219 0 0);
    --primary: oklch(0.6397 0.172 36.4421);
    --primary-foreground: oklch(1 0 0);
    --secondary: oklch(0.3095 0.0266 266.7132);
    --secondary-foreground: oklch(0.9219 0 0);
    --muted: oklch(0.3095 0.0266 266.7132);
    --muted-foreground: oklch(0.7155 0 0);
    --accent: oklch(0.338 0.0589 267.5867);
    --accent-foreground: oklch(0.8823 0.0571 254.1284);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1 0 0);
    --border: oklch(0.3843 0.0301 269.7337);
    --input: oklch(0.3843 0.0301 269.7337);
    --ring: oklch(0.6397 0.172 36.4421);
    --chart-1: oklch(0.7156 0.0605 248.6845);
    --chart-2: oklch(0.7693 0.0876 34.1875);
    --chart-3: oklch(0.5778 0.0759 254.1573);
    --chart-4: oklch(0.5016 0.0849 259.4902);
    --chart-5: oklch(0.4241 0.0952 264.0306);
    --sidebar: oklch(0.31 0.0283 267.7408);
    --sidebar-foreground: oklch(0.9219 0 0);
    --sidebar-primary: oklch(0.6397 0.172 36.4421);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.338 0.0589 267.5867);
    --sidebar-accent-foreground: oklch(0.8823 0.0571 254.1284);
    --sidebar-border: oklch(0.3843 0.0301 269.7337);
    --sidebar-ring: oklch(0.6397 0.172 36.4421);
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.1),
      0px 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: Arial, Helvetica, sans-serif;
  }
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
  }

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
