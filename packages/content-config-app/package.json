{"name": "content-config-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001 --turbo", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next dist"}, "dependencies": {"@auth/prisma-adapter": "^2.5.3", "@aws-sdk/client-s3": "^3.859.0", "@hookform/resolvers": "^5.2.1", "@next/third-parties": "^15.1.5", "@prisma/client": "^6.13.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.59.0", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "lucide-react": "^0.540.0", "next": "^15.4.5", "next-auth": "^5.0.0-beta.29", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.62.0", "react-icons": "^5.3.0", "slugify": "^1.6.6", "stripe": "^17.1.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "0.5.0-alpha.3", "@types/node": "^20", "@types/react": "^19", "eslint": "^8", "eslint-config-next": "^15.4.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "prettier": "3.3.3", "prisma-db": "workspace:*", "tailwindcss": "^4.0.0", "typescript": "^5", "typescript-config": "workspace:*", "ui": "workspace:*"}}