{"name": "ui", "version": "0.1.0", "private": true, "main": "./index.tsx", "types": "./index.tsx", "scripts": {"lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@aws-sdk/client-s3": "^3.664.0", "@langchain/core": "^0.3.10", "@langchain/google-genai": "^0.2.16", "@langchain/openai": "^0.6.3", "@mdxeditor/editor": "^3.40.1", "clsx": "^2.0.0", "next": "^15.4.5", "openai": "^4.67.3", "react": "^19", "react-dom": "^19", "stripe": "^17.1.0", "tailwindcss": "^4.0.0", "zod": "^3.25.76"}, "devDependencies": {"@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "^15.4.5", "typescript": "^5", "typescript-config": "workspace:*"}}